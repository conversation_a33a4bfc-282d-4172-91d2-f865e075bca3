import React from 'react'
import { ActionButton } from './action-button'
import { ModelSelector } from './model-selector'

// Icon assets
const attachIcon = "http://localhost:3845/assets/8001fd601aa5f32848a0bd3d6b1f9b0e2c6f73e9.svg"
const attachIcon2 = "http://localhost:3845/assets/3f8e67c1ac12f72bcedae519afa18e1eef929532.svg"
const folderIcon = "http://localhost:3845/assets/61b108af4a94569f950a4ed4fbd1c185cdbee61a.svg"
const sendIcon = "http://localhost:3845/assets/bf5b1336db5aae2b4a71a71ec77b8a63cd5c72bd.svg"

export function ChatInputBox() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-6 items-center justify-start p-0 relative shrink-0 w-[672px]"
      data-name="input area"
    >
      <div
        className="bg-[#ffffff] h-[122px] relative rounded-2xl shrink-0 w-full"
        data-name="input box_welcome"
      >
        <div className="absolute border border-[#dddddd] border-solid inset-0 pointer-events-none rounded-2xl shadow-[-2px_-2px_16px_0px_rgba(0,0,0,0.05),2px_2px_16px_0px_rgba(0,0,0,0.05)]" />
        <div className="relative size-full">
          <div className="box-border content-stretch flex flex-col gap-10 h-[122px] items-start justify-start p-[16px] relative w-full">
            <div
              className="flex flex-col justify-center leading-[0] not-italic relative shrink-0 text-[#73726c] text-[16px] text-left tracking-[-0.8px] w-full"
              style={{ fontFamily: 'system-ui, -apple-system, sans-serif' }}
            >
              <p className="block leading-[normal]">Reply to claude...</p>
            </div>
            <div
              className="box-border content-stretch flex flex-row items-center justify-between p-0 relative shrink-0 w-full"
              data-name="actions"
            >
              <div
                className="box-border content-stretch flex flex-row gap-2 items-center justify-start p-0 relative shrink-0"
                data-name="actions 1"
              >
                <ActionButton>
                  <div className="relative shrink-0 size-5">
                    <div className="absolute bottom-1/2 left-1/4 right-1/4 top-1/2">
                      <div
                        className="absolute bottom-[-0.75px] left-[-7.5%] right-[-7.5%] top-[-0.75px]"
                        style={{
                          "--stroke-0": "rgba(61, 61, 58, 1)",
                        } as React.CSSProperties}
                      >
                        <img
                          alt="Attach"
                          className="block max-w-none size-full"
                          src={attachIcon}
                        />
                      </div>
                    </div>
                    <div className="absolute bottom-1/4 left-1/2 right-1/2 top-1/4">
                      <div
                        className="absolute bottom-[-7.5%] left-[-0.75px] right-[-0.75px] top-[-7.5%]"
                        style={{
                          "--stroke-0": "rgba(61, 61, 58, 1)",
                        } as React.CSSProperties}
                      >
                        <img
                          alt="Attach"
                          className="block max-w-none size-full"
                          src={attachIcon2}
                        />
                      </div>
                    </div>
                  </div>
                </ActionButton>
                <ActionButton>
                  <div className="overflow-clip relative rounded-[5px] shrink-0 size-5">
                    <div className="absolute bottom-[13.542%] left-[5.208%] right-[5.208%] top-[13.542%]">
                      <img
                        alt="Folder"
                        className="block max-w-none size-full"
                        src={folderIcon}
                      />
                    </div>
                  </div>
                </ActionButton>
              </div>
              <div
                className="box-border content-stretch flex flex-row gap-4 items-center justify-start p-0 relative shrink-0"
                data-name="actions 2"
              >
                <ModelSelector />
                <ActionButton variant="primary">
                  <div className="overflow-clip relative shrink-0 size-5">
                    <div className="absolute bottom-[16.667%] left-1/4 right-1/4 top-[16.667%]">
                      <div
                        className="absolute bottom-[-5.625%] left-[-7.5%] right-[-7.5%] top-[-5.625%]"
                        style={{
                          "--stroke-0": "rgba(255, 255, 255, 1)",
                        } as React.CSSProperties}
                      >
                        <img
                          alt="Send"
                          className="block max-w-none size-full"
                          src={sendIcon}
                        />
                      </div>
                    </div>
                  </div>
                </ActionButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
