import React from 'react'

interface ActionButtonProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'primary'
}

export function ActionButton({ children, className = '', variant = 'default' }: ActionButtonProps) {
  const baseClasses = "box-border content-stretch flex flex-row gap-1 items-center justify-center p-[6px] relative rounded-lg shrink-0 size-8"
  const variantClasses = variant === 'primary' 
    ? "bg-[#e5b2a1]" 
    : ""
  const borderClasses = variant === 'default' 
    ? "border-[#dddddd] border-[0.5px] border-solid" 
    : ""

  return (
    <div className={`${baseClasses} ${variantClasses} ${className}`}>
      {variant === 'default' && (
        <div className={`absolute inset-0 pointer-events-none rounded-lg ${borderClasses}`} />
      )}
      {children}
    </div>
  )
}
