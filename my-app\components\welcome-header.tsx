import React from 'react'

// <PERSON> logo asset
const claudeLogo = "http://localhost:3845/assets/3fa6511efc7f00aad36993f0334ded24a88a5793.svg"

export function WelcomeHeader() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-6 items-center justify-start p-0 relative shrink-0"
      data-name="welcome"
    >
      <div
        className="relative shrink-0 size-8"
        data-name="claude-logo"
      >
        <img
          alt="Claude logo"
          className="block max-w-none size-full"
          src={claudeLogo}
        />
      </div>
      <div
        className="flex flex-col justify-center leading-[0] not-italic relative shrink-0 text-[#000000] text-[40px] text-left text-nowrap"
        style={{ fontFamily: 'Times New Roman, serif' }}
      >
        <p className="block leading-[normal] whitespace-pre">
          Good afternoon, yuxuan
        </p>
      </div>
    </div>
  )
}
