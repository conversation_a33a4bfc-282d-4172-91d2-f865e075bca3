{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/PlaygroundChat2/my-app/components/welcome-header.tsx"], "sourcesContent": ["import React from 'react'\n\n// <PERSON> logo asset\nconst claudeLogo = \"http://localhost:3845/assets/3fa6511efc7f00aad36993f0334ded24a88a5793.svg\"\n\nexport function WelcomeHeader() {\n  return (\n    <div\n      className=\"box-border content-stretch flex flex-row gap-6 items-center justify-start p-0 relative shrink-0\"\n      data-name=\"welcome\"\n    >\n      <div\n        className=\"relative shrink-0 size-8\"\n        data-name=\"claude-logo\"\n      >\n        <img\n          alt=\"Claude logo\"\n          className=\"block max-w-none size-full\"\n          src={claudeLogo}\n        />\n      </div>\n      <div\n        className=\"flex flex-col justify-center leading-[0] not-italic relative shrink-0 text-[#000000] text-[40px] text-left text-nowrap\"\n        style={{ fontFamily: 'Times New Roman, serif' }}\n      >\n        <p className=\"block leading-[normal] whitespace-pre\">\n          Good afternoon, yuxuan\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA,oBAAoB;AACpB,MAAM,aAAa;AAEZ,SAAS;IACd,qBACE,8OAAC;QACC,WAAU;QACV,aAAU;;0BAEV,8OAAC;gBACC,WAAU;gBACV,aAAU;0BAEV,cAAA,8OAAC;oBACC,KAAI;oBACJ,WAAU;oBACV,KAAK;;;;;;;;;;;0BAGT,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAyB;0BAE9C,cAAA,8OAAC;oBAAE,WAAU;8BAAwC;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/PlaygroundChat2/my-app/components/action-button.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface ActionButtonProps {\n  children: React.ReactNode\n  className?: string\n  variant?: 'default' | 'primary'\n}\n\nexport function ActionButton({ children, className = '', variant = 'default' }: ActionButtonProps) {\n  const baseClasses = \"box-border content-stretch flex flex-row gap-1 items-center justify-center p-[6px] relative rounded-lg shrink-0 size-8\"\n  const variantClasses = variant === 'primary' \n    ? \"bg-[#e5b2a1]\" \n    : \"\"\n  const borderClasses = variant === 'default' \n    ? \"border-[#dddddd] border-[0.5px] border-solid\" \n    : \"\"\n\n  return (\n    <div className={`${baseClasses} ${variantClasses} ${className}`}>\n      {variant === 'default' && (\n        <div className={`absolute inset-0 pointer-events-none rounded-lg ${borderClasses}`} />\n      )}\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,EAAqB;IAC/F,MAAM,cAAc;IACpB,MAAM,iBAAiB,YAAY,YAC/B,iBACA;IACJ,MAAM,gBAAgB,YAAY,YAC9B,iDACA;IAEJ,qBACE,8OAAC;QAAI,WAAW,GAAG,YAAY,CAAC,EAAE,eAAe,CAAC,EAAE,WAAW;;YAC5D,YAAY,2BACX,8OAAC;gBAAI,WAAW,CAAC,gDAAgD,EAAE,eAAe;;;;;;YAEnF;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/PlaygroundChat2/my-app/components/model-selector.tsx"], "sourcesContent": ["import React from 'react'\n\n// Arrow down icon asset\nconst arrowDownIcon = \"http://localhost:3845/assets/b1bf69ea1ff4334ec4a678a5b8418d27d7b6c688.svg\"\n\nexport function ModelSelector() {\n  return (\n    <div\n      className=\"box-border content-stretch flex flex-row gap-1 items-center justify-center p-0 relative shrink-0 w-[116px]\"\n      data-name=\"model-selector\"\n    >\n      <div\n        className=\"flex flex-col justify-center leading-[0] not-italic relative shrink-0 text-[#434342] text-[14px] text-left text-nowrap\"\n        style={{ fontFamily: 'Times New Roman, serif' }}\n      >\n        <p className=\"block leading-[normal] whitespace-pre\">\n          <PERSON> 4\n        </p>\n      </div>\n      <div\n        className=\"overflow-clip relative shrink-0 size-4\"\n        data-name=\"arrow-down\"\n      >\n        <div\n          className=\"absolute bottom-[37.5%] left-[20.833%] right-[20.833%] top-[37.5%]\"\n          data-name=\"Vector\"\n        >\n          <div\n            className=\"absolute bottom-[-18.75%] left-[-8.036%] right-[-8.036%] top-[-18.75%]\"\n            style={{\n              \"--stroke-0\": \"rgba(132, 131, 125, 1)\",\n            } as React.CSSProperties}\n          >\n            <img\n              alt=\"Dropdown arrow\"\n              className=\"block max-w-none size-full\"\n              src={arrowDownIcon}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA,wBAAwB;AACxB,MAAM,gBAAgB;AAEf,SAAS;IACd,qBACE,8OAAC;QACC,WAAU;QACV,aAAU;;0BAEV,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAyB;0BAE9C,cAAA,8OAAC;oBAAE,WAAU;8BAAwC;;;;;;;;;;;0BAIvD,8OAAC;gBACC,WAAU;gBACV,aAAU;0BAEV,cAAA,8OAAC;oBACC,WAAU;oBACV,aAAU;8BAEV,cAAA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,cAAc;wBAChB;kCAEA,cAAA,8OAAC;4BACC,KAAI;4BACJ,WAAU;4BACV,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/PlaygroundChat2/my-app/components/chat-input-box.tsx"], "sourcesContent": ["import React from 'react'\nimport { ActionButton } from './action-button'\nimport { ModelSelector } from './model-selector'\n\n// Icon assets\nconst attachIcon = \"http://localhost:3845/assets/8001fd601aa5f32848a0bd3d6b1f9b0e2c6f73e9.svg\"\nconst attachIcon2 = \"http://localhost:3845/assets/3f8e67c1ac12f72bcedae519afa18e1eef929532.svg\"\nconst folderIcon = \"http://localhost:3845/assets/61b108af4a94569f950a4ed4fbd1c185cdbee61a.svg\"\nconst sendIcon = \"http://localhost:3845/assets/bf5b1336db5aae2b4a71a71ec77b8a63cd5c72bd.svg\"\n\nexport function ChatInputBox() {\n  return (\n    <div\n      className=\"box-border content-stretch flex flex-col gap-6 items-center justify-start p-0 relative shrink-0 w-[672px]\"\n      data-name=\"input area\"\n    >\n      <div\n        className=\"bg-[#ffffff] h-[122px] relative rounded-2xl shrink-0 w-full\"\n        data-name=\"input box_welcome\"\n      >\n        <div className=\"absolute border border-[#dddddd] border-solid inset-0 pointer-events-none rounded-2xl shadow-[-2px_-2px_16px_0px_rgba(0,0,0,0.05),2px_2px_16px_0px_rgba(0,0,0,0.05)]\" />\n        <div className=\"relative size-full\">\n          <div className=\"box-border content-stretch flex flex-col gap-10 h-[122px] items-start justify-start p-[16px] relative w-full\">\n            <div\n              className=\"flex flex-col justify-center leading-[0] not-italic relative shrink-0 text-[#73726c] text-[16px] text-left tracking-[-0.8px] w-full\"\n              style={{ fontFamily: 'system-ui, -apple-system, sans-serif' }}\n            >\n              <p className=\"block leading-[normal]\">Reply to claude...</p>\n            </div>\n            <div\n              className=\"box-border content-stretch flex flex-row items-center justify-between p-0 relative shrink-0 w-full\"\n              data-name=\"actions\"\n            >\n              <div\n                className=\"box-border content-stretch flex flex-row gap-2 items-center justify-start p-0 relative shrink-0\"\n                data-name=\"actions 1\"\n              >\n                <ActionButton>\n                  <div className=\"relative shrink-0 size-5\">\n                    <div className=\"absolute bottom-1/2 left-1/4 right-1/4 top-1/2\">\n                      <div\n                        className=\"absolute bottom-[-0.75px] left-[-7.5%] right-[-7.5%] top-[-0.75px]\"\n                        style={{\n                          \"--stroke-0\": \"rgba(61, 61, 58, 1)\",\n                        } as React.CSSProperties}\n                      >\n                        <img\n                          alt=\"Attach\"\n                          className=\"block max-w-none size-full\"\n                          src={attachIcon}\n                        />\n                      </div>\n                    </div>\n                    <div className=\"absolute bottom-1/4 left-1/2 right-1/2 top-1/4\">\n                      <div\n                        className=\"absolute bottom-[-7.5%] left-[-0.75px] right-[-0.75px] top-[-7.5%]\"\n                        style={{\n                          \"--stroke-0\": \"rgba(61, 61, 58, 1)\",\n                        } as React.CSSProperties}\n                      >\n                        <img\n                          alt=\"Attach\"\n                          className=\"block max-w-none size-full\"\n                          src={attachIcon2}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </ActionButton>\n                <ActionButton>\n                  <div className=\"overflow-clip relative rounded-[5px] shrink-0 size-5\">\n                    <div className=\"absolute bottom-[13.542%] left-[5.208%] right-[5.208%] top-[13.542%]\">\n                      <img\n                        alt=\"Folder\"\n                        className=\"block max-w-none size-full\"\n                        src={folderIcon}\n                      />\n                    </div>\n                  </div>\n                </ActionButton>\n              </div>\n              <div\n                className=\"box-border content-stretch flex flex-row gap-4 items-center justify-start p-0 relative shrink-0\"\n                data-name=\"actions 2\"\n              >\n                <ModelSelector />\n                <ActionButton variant=\"primary\">\n                  <div className=\"overflow-clip relative shrink-0 size-5\">\n                    <div className=\"absolute bottom-[16.667%] left-1/4 right-1/4 top-[16.667%]\">\n                      <div\n                        className=\"absolute bottom-[-5.625%] left-[-7.5%] right-[-7.5%] top-[-5.625%]\"\n                        style={{\n                          \"--stroke-0\": \"rgba(255, 255, 255, 1)\",\n                        } as React.CSSProperties}\n                      >\n                        <img\n                          alt=\"Send\"\n                          className=\"block max-w-none size-full\"\n                          src={sendIcon}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </ActionButton>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,cAAc;AACd,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,WAAW;AAEV,SAAS;IACd,qBACE,8OAAC;QACC,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAU;YACV,aAAU;;8BAEV,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,YAAY;gCAAuC;0CAE5D,cAAA,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;0CAExC,8OAAC;gCACC,WAAU;gCACV,aAAU;;kDAEV,8OAAC;wCACC,WAAU;wCACV,aAAU;;0DAEV,8OAAC,+HAAA,CAAA,eAAY;0DACX,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,cAAc;gEAChB;0EAEA,cAAA,8OAAC;oEACC,KAAI;oEACJ,WAAU;oEACV,KAAK;;;;;;;;;;;;;;;;sEAIX,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,cAAc;gEAChB;0EAEA,cAAA,8OAAC;oEACC,KAAI;oEACJ,WAAU;oEACV,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMf,8OAAC,+HAAA,CAAA,eAAY;0DACX,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAI;4DACJ,WAAU;4DACV,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMf,8OAAC;wCACC,WAAU;wCACV,aAAU;;0DAEV,8OAAC,gIAAA,CAAA,gBAAa;;;;;0DACd,8OAAC,+HAAA,CAAA,eAAY;gDAAC,SAAQ;0DACpB,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,cAAc;4DAChB;sEAEA,cAAA,8OAAC;gEACC,KAAI;gEACJ,WAAU;gEACV,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa/B", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/PlaygroundChat2/my-app/app/page.tsx"], "sourcesContent": ["import React from 'react'\r\nimport { WelcomeHeader } from '../components/welcome-header'\r\nimport { ChatInputBox } from '../components/chat-input-box'\r\n\r\nexport default function Home() {\r\n  return (\r\n    <div\r\n      className=\"bg-[#faf9f5] box-border content-stretch flex flex-row items-center justify-start overflow-clip p-0 relative rounded-2xl size-full\"\r\n      data-name=\"Claude_Prototype\"\r\n    >\r\n      <div\r\n        className=\"box-border content-stretch flex flex-col gap-6 h-[800px] items-center justify-center pb-[200px] pt-20 px-2.5 relative shrink-0 w-[1420px]\"\r\n        data-name=\"main area 1\"\r\n      >\r\n        <WelcomeHeader />\r\n        <ChatInputBox />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAU;YACV,aAAU;;8BAEV,8OAAC,gIAAA,CAAA,gBAAa;;;;;8BACd,8OAAC,mIAAA,CAAA,eAAY;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}]}