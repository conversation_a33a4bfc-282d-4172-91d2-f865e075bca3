import React from 'react'

// Arrow down icon asset
const arrowDownIcon = "http://localhost:3845/assets/b1bf69ea1ff4334ec4a678a5b8418d27d7b6c688.svg"

export function ModelSelector() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-1 items-center justify-center p-0 relative shrink-0 w-[116px]"
      data-name="model-selector"
    >
      <div
        className="flex flex-col justify-center leading-[0] not-italic relative shrink-0 text-[#434342] text-[14px] text-left text-nowrap"
        style={{ fontFamily: 'Times New Roman, serif' }}
      >
        <p className="block leading-[normal] whitespace-pre">
          <PERSON> 4
        </p>
      </div>
      <div
        className="overflow-clip relative shrink-0 size-4"
        data-name="arrow-down"
      >
        <div
          className="absolute bottom-[37.5%] left-[20.833%] right-[20.833%] top-[37.5%]"
          data-name="Vector"
        >
          <div
            className="absolute bottom-[-18.75%] left-[-8.036%] right-[-8.036%] top-[-18.75%]"
            style={{
              "--stroke-0": "rgba(132, 131, 125, 1)",
            } as React.CSSProperties}
          >
            <img
              alt="Dropdown arrow"
              className="block max-w-none size-full"
              src={arrowDownIcon}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
